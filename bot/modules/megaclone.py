#!/usr/bin/env python3
"""
MEGA Clone Command Handler
Dedicated command for MEGA-to-MEGA cloning operations
"""

from asyncio import create_task
from bot.core.config_manager import Config
from bot.helper.ext_utils.bot_utils import (
    arg_parser,
    get_content_type,
    new_task,
    sync_to_async,
)
from bot.helper.ext_utils.exceptions import DirectDownloadLinkException
from bot.helper.ext_utils.links_utils import is_mega_link
from bot.helper.ext_utils.task_manager import check_running_tasks, stop_duplicate_check
from bot.helper.listeners.task_listener import TaskListener
from bot.helper.mirror_leech_utils.download_utils.mega_clone import add_mega_clone
from bot.helper.telegram_helper.message_utils import (
    auto_delete_message,
    delete_links,
    send_message,
)


class MegaClone(TaskListener):
    """MEGA Clone Task Listener"""
    
    def __init__(self, client, message, bulk=None, multi_tag=None, options=""):
        if bulk is None:
            bulk = []
        self.message = message
        self.client = client
        self.multi_tag = multi_tag
        self.options = options
        self.bulk = bulk
        super().__init__()

    @new_task
    async def new_event(self):
        """Handle new MEGA clone event"""
        text = self.message.text.split("\n")
        input_list = text[0].split(" ")
        
        # Parse arguments
        args = {
            "link": "",
            "-i": 0,
            "-b": False,
            "-n": "",
        }
        
        arg_parser(input_list[1:], args)
        
        try:
            self.multi = int(args["-i"])
        except Exception:
            self.multi = 0
        
        self.link = args["link"]
        self.name = args["-n"]
        
        # Validate MEGA link
        if not self.link:
            msg = await send_message(
                self.message,
                "❌ No MEGA link provided!\n\n"
                "<b>Usage:</b> <code>/megaclone MEGA_LINK</code>\n"
                "<b>Example:</b> <code>/megaclone https://mega.nz/file/ABC123</code>"
            )
            await delete_links(self.message)
            return await auto_delete_message(msg, time=300)
        
        if not is_mega_link(self.link):
            msg = await send_message(
                self.message,
                "❌ Invalid MEGA link!\n\n"
                "Please provide a valid MEGA.nz link.\n"
                "<b>Example:</b> <code>https://mega.nz/file/ABC123</code>"
            )
            await delete_links(self.message)
            return await auto_delete_message(msg, time=300)
        
        # Check if MEGA operations are enabled
        if not Config.MEGA_ENABLED:
            msg = await send_message(
                self.message,
                "❌ MEGA.nz operations are disabled by the administrator."
            )
            await delete_links(self.message)
            return await auto_delete_message(msg, time=300)

        # Check if MEGA clone is enabled
        if not Config.MEGA_CLONE_ENABLED:
            msg = await send_message(
                self.message,
                "❌ MEGA clone operations are disabled by the administrator."
            )
            await delete_links(self.message)
            return await auto_delete_message(msg, time=300)
        
        # Check if MEGA credentials are configured
        if not Config.MEGA_EMAIL or not Config.MEGA_PASSWORD:
            msg = await send_message(
                self.message,
                "❌ MEGA credentials not configured. Please contact the administrator."
            )
            await delete_links(self.message)
            return await auto_delete_message(msg, time=300)
        
        # Set default name if not provided
        if not self.name:
            try:
                # Extract filename from MEGA link if possible
                if "/file/" in self.link:
                    self.name = "MEGA_File"
                elif "/folder/" in self.link:
                    self.name = "MEGA_Folder"
                else:
                    self.name = "MEGA_Clone"
            except Exception:
                self.name = "MEGA_Clone"
        
        # Start MEGA clone operation
        await self.on_download_start()
        create_task(add_mega_clone(self, self.link))
        await delete_links(self.message)


async def mega_clone(client, message):
    """
    MEGA Clone command handler
    
    Clones files/folders directly from MEGA to MEGA account without downloading locally.
    This is faster and more efficient than downloading and re-uploading.
    """
    # Check if MEGA operations are enabled
    if not Config.MEGA_ENABLED:
        await send_message(
            message,
            "❌ MEGA.nz operations are disabled by the administrator."
        )
        return

    # Check if MEGA clone operations are enabled
    if not Config.MEGA_CLONE_ENABLED:
        await send_message(
            message,
            "❌ MEGA clone operations are disabled by the administrator."
        )
        return
    
    # Check if MEGA credentials are configured
    if not Config.MEGA_EMAIL or not Config.MEGA_PASSWORD:
        await send_message(
            message,
            "❌ MEGA credentials not configured. Please contact the administrator."
        )
        return
    
    # Create MEGA clone task
    create_task(MegaClone(client, message).new_event())


# Handler registration is done in bot/core/handlers.py to avoid circular imports
