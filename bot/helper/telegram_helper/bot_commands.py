# ruff: noqa: RUF012
from bot.core.config_manager import Config

i = Config.CMD_SUFFIX


class BotCommands:
    StartCommand = "start"
    MirrorCommand = [f"mirror{i}", f"m{i}"]
    JdMirrorCommand = [f"jdmirror{i}", f"jm{i}"]
    NzbMirrorCommand = [f"nzbmirror{i}", f"nm{i}"]
    YtdlCommand = [f"ytdl{i}", f"y{i}"]
    LeechCommand = [f"leech{i}", f"l{i}"]
    JdLeechCommand = [f"jdleech{i}", f"jl{i}"]
    NzbLeechCommand = [f"nzbleech{i}", f"nl{i}"]
    YtdlLeechCommand = [f"ytdlleech{i}", f"yl{i}"]
    CloneCommand = f"clone{i}"
    MegaCloneCommand = [f"megaclone{i}", f"mc{i}"]
    MediaInfoCommand = [f"mediainfo{i}", f"mi{i}"]
    CountCommand = f"count{i}"
    DeleteCommand = f"del{i}"
    CancelAllCommand = f"cancelall{i}"
    ForceStartCommand = [f"forcestart{i}", f"fs{i}"]
    ListCommand = f"list{i}"
    SearchCommand = f"search{i}"
    HydraSearchCommamd = f"nzbsearch{i}"
    StatusCommand = [f"status{i}", f"s{i}", "statusall", "sall"]
    UsersCommand = f"users{i}"
    AuthorizeCommand = f"auth{i}"
    UnAuthorizeCommand = f"unauth{i}"
    AddSudoCommand = f"addsudo{i}"
    RmSudoCommand = f"rmsudo{i}"
    PingCommand = f"ping{i}"
    RestartCommand = [f"restart{i}", "restartall"]
    StatsCommand = f"stats{i}"
    HelpCommand = f"help{i}"
    LogCommand = f"log{i}"
    ShellCommand = f"shell{i}"
    AExecCommand = f"aexec{i}"
    ExecCommand = f"exec{i}"
    ClearLocalsCommand = f"clearlocals{i}"
    BotSetCommand = f"botsettings{i}"
    UserSetCommand = [f"settings{i}", f"usettings{i}", f"us{i}"]
    SpeedTest = f"speedtest{i}"
    BroadcastCommand = [f"broadcast{i}", "broadcastall"]
    SelectCommand = f"sel{i}"
    RssCommand = f"rss{i}"
    FontStylesCommand = [f"fontstyles{i}", f"fonts{i}"]
    CheckDeletionsCommand = [f"check_deletions{i}", f"cd{i}"]
    IMDBCommand = f"imdb{i}"
    LoginCommand = f"login{i}"
    MediaSearchCommand = [f"mediasearch{i}", f"mds{i}"]
    MediaToolsCommand = [f"mediatools{i}", f"mt{i}"]
    MediaToolsHelpCommand = [f"mthelp{i}", f"mth{i}"]
    GenSessionCommand = [f"gensession{i}", f"gs{i}"]
    TruecallerCommand = f"truecaller{i}"
    AskCommand = f"ask{i}"
    SoxCommand = [f"spectrum{i}", f"sox{i}"]
    PasteCommand = f"paste{i}"
    VirusTotalCommand = f"virustotal{i}"
    StreamripMirrorCommand = [f"streamripmirror{i}", f"srmirror{i}", f"srm{i}"]
    StreamripLeechCommand = [f"streamripleech{i}", f"srleech{i}", f"srl{i}"]
    StreamripSearchCommand = [f"streamripsearch{i}", f"srsearch{i}", f"srs{i}"]
    ZotifyMirrorCommand = [f"zotifymirror{i}", f"zmirror{i}", f"zm{i}"]
    ZotifyLeechCommand = [f"zotifyleech{i}", f"zleech{i}", f"zl{i}"]
    ZotifySearchCommand = [f"zotifysearch{i}", f"zsearch{i}", f"zs{i}"]
