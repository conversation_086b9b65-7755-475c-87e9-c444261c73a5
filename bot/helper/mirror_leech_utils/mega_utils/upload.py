from secrets import token_hex
from aiofiles.os import makedirs, path as aiopath, remove as aioremove
from asyncio import Event, create_subprocess_exec
import asyncio
from time import time
from os import path as ospath, walk
import subprocess

# Try to import MEGA SDK at module level, but don't fail if not available
try:
    from mega import <PERSON>Api, MegaListener, MegaRequest, MegaTransfer, MegaError
    MEGA_SDK_AVAILABLE = True
    MEGA_IMPORT_ERROR = None
except ImportError as e:
    # Create dummy classes to prevent import errors
    class MegaApi: pass
    class MegaListener: pass
    class MegaRequest:
        TYPE_LOGIN = 1
        TYPE_FETCH_NODES = 2
        TYPE_GET_PUBLIC_NODE = 3
        TYPE_COPY = 4
        TYPE_EXPORT = 5
        TYPE_CREATE_FOLDER = 6
    class MegaTransfer: pass
    class MegaError: pass
    MEGA_SDK_AVAILABLE = False
    MEGA_IMPORT_ERROR = str(e)
except Exception as e:
    # Handle other import errors
    class MegaApi: pass
    class MegaListener: pass
    class MegaRequest:
        TYPE_LOGIN = 1
        TYPE_FETCH_NODES = 2
        TYPE_GET_PUBLIC_NODE = 3
        TYPE_COPY = 4
        TYPE_EXPORT = 5
        TYPE_CREATE_FOLDER = 6
    class MegaTransfer: pass
    class MegaError: pass
    MEGA_SDK_AVAILABLE = False
    MEGA_IMPORT_ERROR = f"Unexpected error: {str(e)}"

from bot import LOGGER, task_dict, task_dict_lock
from bot.core.config_manager import Config
from bot.helper.ext_utils.bot_utils import sync_to_async, async_to_sync
from bot.helper.ext_utils.task_manager import check_running_tasks
from bot.helper.mirror_leech_utils.status_utils.mega_status import MegaUploadStatus
from bot.helper.mirror_leech_utils.status_utils.queue_status import QueueStatus
from bot.helper.telegram_helper.message_utils import send_status_message


async def generate_video_thumbnail(video_path):
    """
    Generate thumbnail for video files using FFmpeg.

    Args:
        video_path: Path to the video file

    Returns:
        Path to generated thumbnail or None if failed
    """
    if not Config.MEGA_UPLOAD_THUMBNAIL:
        return None

    try:
        # Check if file is a video
        video_extensions = [
            ".mp4",
            ".mkv",
            ".avi",
            ".mov",
            ".wmv",
            ".flv",
            ".webm",
            ".m4v",
        ]
        if not any(video_path.lower().endswith(ext) for ext in video_extensions):
            return None

        thumbnail_path = f"{video_path}_thumbnail.jpg"

        # Use FFmpeg to generate thumbnail at 10% of video duration
        cmd = [
            "xtra",  # Using the renamed ffmpeg binary
            "-i",
            video_path,
            "-ss",
            "00:00:10",  # Seek to 10 seconds
            "-vframes",
            "1",  # Extract 1 frame
            "-vf",
            "scale=320:240",  # Resize to 320x240
            "-y",  # Overwrite output file
            thumbnail_path,
        ]

        LOGGER.info(f"Generating thumbnail for: {video_path}")
        process = await create_subprocess_exec(
            *cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE
        )
        stdout, stderr = await process.communicate()

        if process.returncode == 0 and ospath.exists(thumbnail_path):
            LOGGER.info(f"Thumbnail generated successfully: {thumbnail_path}")
            return thumbnail_path
        else:
            LOGGER.error(f"Failed to generate thumbnail: {stderr.decode()}")
            return None

    except Exception as e:
        LOGGER.error(f"Error generating thumbnail: {e}")
        return None


class MegaUploadListener(MegaListener):
    _NO_EVENT_ON = (MegaRequest.TYPE_LOGIN, MegaRequest.TYPE_FETCH_NODES)
    NO_ERROR = "no error"

    def __init__(self, continue_event: Event, listener):
        super().__init__()  # Call parent constructor first
        self.continue_event = continue_event
        self.node = None
        self.upload_node = None
        self.listener = listener
        self.is_cancelled = False
        self.error = None
        self.__bytes_transferred = 0
        self.__speed = 0
        self.__name = ""
        self.__start_time = time()
        self.__upload_folder = None

    @property
    def downloaded_bytes(self):
        return self.__bytes_transferred

    @property
    def speed(self):
        return self.__speed

    async def cancel_download(self):
        self.is_cancelled = True
        await self.listener.on_upload_error("Upload cancelled by user")

    def onRequestFinish(self, api, request, error):
        try:
            # More robust error checking
            error_str = str(error).lower() if error else "unknown error"
            if error_str != "no error":
                self.error = error.copy() if hasattr(error, 'copy') else str(error)
                LOGGER.error(f"MEGA Upload Request Error: {self.error}")
                self.continue_event.set()
                return

            # Safer request type checking
            try:
                request_type = request.getType()
            except Exception as e:
                LOGGER.error(f"Failed to get request type: {e}")
                self.error = f"Request type error: {e}"
                self.continue_event.set()
                return

            if request_type == MegaRequest.TYPE_LOGIN:
                LOGGER.info("MEGA login successful for upload, fetching nodes...")
                try:
                    api.fetchNodes()
                except Exception as e:
                    LOGGER.error(f"Failed to fetch nodes: {e}")
                    self.error = f"Fetch nodes error: {e}"
                    self.continue_event.set()

            elif request_type == MegaRequest.TYPE_FETCH_NODES:
                LOGGER.info("MEGA nodes fetched for upload, getting root node...")
                try:
                    self.node = api.getRootNode()
                    if self.node:
                        self.__name = self.node.getName()
                        LOGGER.info(f"MEGA root node for upload: {self.node.getName()}")
                    else:
                        self.error = "Failed to get root node"
                        LOGGER.error("MEGA upload: Failed to get root node")
                except Exception as e:
                    LOGGER.error(f"Failed to get root node: {e}")
                    self.error = f"Root node error: {e}"

            elif request_type == MegaRequest.TYPE_CREATE_FOLDER:
                try:
                    self.__upload_folder = request.getNodeHandle()
                    LOGGER.info(f"MEGA upload folder created: {self.__upload_folder}")
                except Exception as e:
                    LOGGER.error(f"Failed to get folder handle: {e}")
                    self.error = f"Folder handle error: {e}"

            elif request_type == MegaRequest.TYPE_EXPORT:
                try:
                    public_link = request.getLink()
                    LOGGER.info(f"MEGA public link generated: {public_link}")
                    async_to_sync(self.listener.on_upload_complete, public_link)
                except Exception as e:
                    LOGGER.error(f"Failed to get public link: {e}")
                    self.error = f"Public link error: {e}"

            # Safer event setting
            try:
                if request_type not in self._NO_EVENT_ON or (
                    self.node
                    and self.__name
                    and "cloud drive" not in self.__name.lower()
                ):
                    self.continue_event.set()
            except Exception as e:
                LOGGER.error(f"Error setting continue event: {e}")
                self.continue_event.set()

        except Exception as e:
            LOGGER.error(f"Critical exception in MEGA upload onRequestFinish: {e}")
            self.error = f"Critical error: {e}"
            self.continue_event.set()

    def onRequestTemporaryError(self, _api, _request, error: MegaError):
        error_msg = error.toString()
        LOGGER.error(f"MEGA Upload Request temporary error: {error_msg}")
        if not self.is_cancelled:
            self.is_cancelled = True
            async_to_sync(
                self.listener.on_upload_error,
                f"MEGA Upload Request Error: {error_msg}",
            )
        self.error = error_msg
        self.continue_event.set()

    def onTransferUpdate(self, api: MegaApi, transfer: MegaTransfer):
        if self.is_cancelled:
            LOGGER.info("MEGA upload cancelled, stopping transfer...")
            api.cancelTransfer(transfer, None)
            self.continue_event.set()
            return

        self.__speed = transfer.getSpeed()
        self.__bytes_transferred = transfer.getTransferredBytes()

    def onTransferFinish(self, api: MegaApi, transfer: MegaTransfer, error):
        try:
            if self.is_cancelled:
                LOGGER.info("MEGA upload transfer was cancelled")
                self.continue_event.set()
                return

            if str(error).lower() != "no error":
                error_msg = str(error)
                LOGGER.error(
                    f"MEGA upload transfer finished with error: {error_msg}"
                )
                self.error = error_msg
                async_to_sync(
                    self.listener.on_upload_error,
                    f"MEGA Upload Transfer Error: {error_msg}",
                )
            else:
                LOGGER.info(
                    f"MEGA upload completed successfully: {transfer.getFileName()}"
                )
                self.upload_node = transfer.getNodeHandle()

                # Generate public link if configured
                if Config.MEGA_UPLOAD_PUBLIC:
                    node = api.getNodeByHandle(self.upload_node)
                    if node:
                        api.exportNode(node)
                        return  # Wait for export to complete

                # If no public link needed, complete upload
                async_to_sync(self.listener.on_upload_complete, None)

            self.continue_event.set()
        except Exception as e:
            LOGGER.error(f"Exception in MEGA upload onTransferFinish: {e}")
            self.continue_event.set()

    def onTransferTemporaryError(self, _api, transfer, error):
        filename = transfer.getFileName()
        state = transfer.getState()
        error_str = error.toString()

        LOGGER.error(
            f"MEGA upload transfer temporary error for {filename}: {error_str} (state: {state})"
        )

        # States 1 and 4 are recoverable, don't cancel for these
        if state in [1, 4]:
            LOGGER.info(
                f"MEGA upload transfer error is recoverable (state {state}), continuing..."
            )
            return

        self.error = error_str
        if not self.is_cancelled:
            self.is_cancelled = True
            async_to_sync(
                self.listener.on_upload_error,
                f"MEGA Upload Transfer Error: {error_str} ({filename})",
            )
            self.continue_event.set()


class AsyncMegaExecutor:
    def __init__(self):
        self.continue_event = Event()

    async def do(self, function, args, timeout=300):
        """Execute MEGA function with timeout"""
        self.continue_event.clear()
        await sync_to_async(function, *args)
        try:
            await asyncio.wait_for(self.continue_event.wait(), timeout=timeout)
        except asyncio.TimeoutError:
            LOGGER.error(f"MEGA operation timed out after {timeout} seconds")
            raise Exception(f"MEGA operation timed out after {timeout} seconds")


def _get_premium_settings(is_premium):
    """Get optimized settings based on account type"""
    if is_premium:
        return {
            'chunk_size': ********,  # 10MB chunks for premium
            'parallel_uploads': 6,   # More parallel uploads
            'timeout_multiplier': 2, # Longer timeouts for larger transfers
            'bandwidth_limit': 0     # No bandwidth limit
        }
    else:
        return {
            'chunk_size': 1048576,   # 1MB chunks for free
            'parallel_uploads': 1,   # Single upload for free
            'timeout_multiplier': 1, # Standard timeouts
            'bandwidth_limit': 1     # Bandwidth limited
        }


async def _detect_mega_account_type(api):
    """Detect MEGA account type (free/premium) and return settings"""
    try:
        LOGGER.info("Detecting MEGA account type...")

        # Get account details
        account_details = api.getAccountDetails()
        if account_details:
            pro_level = account_details.getProLevel()
            storage_used = account_details.getStorageUsed()
            storage_max = account_details.getStorageMax()
            transfer_used = account_details.getTransferOwnUsed()
            transfer_max = account_details.getTransferMax()

            is_premium = pro_level > 0

            if is_premium:
                LOGGER.info(f"🎉 MEGA Premium account detected (Level {pro_level})")
                LOGGER.info(f"📊 Storage: {storage_used}/{storage_max} bytes")
                LOGGER.info(f"📊 Transfer: {transfer_used}/{transfer_max} bytes")
                LOGGER.info("✅ Premium features enabled: larger chunks, parallel uploads, extended timeouts")
            else:
                LOGGER.info("📱 MEGA Free account detected")
                LOGGER.info(f"📊 Storage: {storage_used}/{storage_max} bytes")
                LOGGER.info(f"📊 Transfer: {transfer_used}/{transfer_max} bytes")
                LOGGER.info("⚠️ Free account limitations: smaller chunks, single uploads, standard timeouts")

            return is_premium, _get_premium_settings(is_premium)
        else:
            LOGGER.warning("Could not retrieve MEGA account details, using free account settings")
            return False, _get_premium_settings(False)

    except Exception as e:
        LOGGER.error(f"Error detecting MEGA account type: {e}")
        LOGGER.info("Using free account settings as fallback")
        return False, _get_premium_settings(False)


async def _cleanup_mega_upload_api(api, executor, listener=None):
    """Helper function to cleanup MEGA API instances"""
    try:
        if listener and api:
            try:
                api.removeListener(listener)
                LOGGER.info("MEGA upload listener removed")
            except Exception as e:
                LOGGER.error(f"Error removing MEGA upload listener: {e}")

        if api and executor:
            try:
                await executor.do(api.logout, ())
                LOGGER.info("MEGA upload API logged out")
            except Exception as e:
                LOGGER.error(f"Error during MEGA upload logout: {e}")
    except Exception as e:
        LOGGER.error(f"Error during MEGA upload API cleanup: {e}")


async def add_mega_upload(listener, path):
    """
    Upload files/folders to MEGA.nz using the MEGA SDK v4.8.0 with FFmpeg support.

    Args:
        listener: Upload listener object
        path: Local path to upload
    """
    # Check if MEGA operations are enabled
    if not Config.MEGA_ENABLED:
        await listener.on_upload_error(
            "❌ MEGA.nz operations are disabled by the administrator."
        )
        return

    # Check if MEGA upload operations are enabled
    if not Config.MEGA_UPLOAD_ENABLED:
        await listener.on_upload_error(
            "❌ MEGA.nz upload operations are disabled by the administrator."
        )
        return

    MEGA_EMAIL = Config.MEGA_EMAIL
    MEGA_PASSWORD = Config.MEGA_PASSWORD

    if not MEGA_EMAIL or not MEGA_PASSWORD:
        await listener.on_upload_error(
            "❌ MEGA.nz credentials not configured. Please set MEGA_EMAIL and MEGA_PASSWORD."
        )
        return

    # Check if MEGA SDK v4.8.0 is available and working
    if not MEGA_SDK_AVAILABLE:
        error_msg = "❌ MEGA SDK v4.8.0 not available. Please install megasdk v4.8.0."
        if MEGA_IMPORT_ERROR:
            error_msg += f"\nImport error: {MEGA_IMPORT_ERROR}"
        await listener.on_upload_error(error_msg)
        return

    LOGGER.info("MEGA SDK v4.8.0 is available for upload")

    LOGGER.info(f"Starting MEGA upload: {path}")

    executor = AsyncMegaExecutor()
    api = None
    mega_listener = None

    try:
        # Initialize MEGA API with error checking and safer patterns
        api_attempts = 3
        api = None

        for attempt in range(api_attempts):
            try:
                # Use a unique app key for each instance to avoid conflicts
                app_key = f"aimleechbot_upload_{int(time())}_{attempt}"
                LOGGER.info(f"Attempting MEGA API creation (attempt {attempt + 1}/{api_attempts})")

                api = MegaApi(None, None, None, app_key)
                if not api:
                    raise Exception("Failed to create MEGA API instance")

                LOGGER.info(f"MEGA API instance created successfully with key: {app_key}")

                # Add a small delay to allow API initialization
                await asyncio.sleep(0.2)
                break

            except Exception as e:
                LOGGER.warning(f"MEGA API creation attempt {attempt + 1} failed: {e}")
                if attempt == api_attempts - 1:
                    error_msg = f"Failed to initialize MEGA API after {api_attempts} attempts: {e!s}"
                    LOGGER.error(error_msg)
                    await listener.on_upload_error(error_msg)
                    return
                else:
                    # Wait before retry
                    await asyncio.sleep(1.0)
                    continue

        try:
            mega_listener = MegaUploadListener(executor.continue_event, listener)
            api.addListener(mega_listener)
            LOGGER.info("MEGA upload listener attached")
        except Exception as e:
            error_msg = f"Failed to attach MEGA listener: {str(e)}"
            LOGGER.error(error_msg)
            await listener.on_upload_error(error_msg)
            return

        # Login with credentials
        LOGGER.info("Logging into MEGA for upload...")
        try:
            await executor.do(api.login, (MEGA_EMAIL, MEGA_PASSWORD), timeout=60)
        except Exception as e:
            error_msg = f"MEGA login failed: {str(e)}"
            LOGGER.error(error_msg)
            await listener.on_upload_error(error_msg)
            return

        if mega_listener.error is not None:
            error_msg = f"Failed to login to MEGA: {mega_listener.error}"
            LOGGER.error(error_msg)
            await listener.on_upload_error(error_msg)
            return

        # Verify we have a valid node
        if not mega_listener.node:
            error_msg = "Failed to get MEGA root node for upload"
            LOGGER.error(error_msg)
            await listener.on_upload_error(error_msg)
            return

        # Detect account type and get optimized settings
        try:
            is_premium, premium_settings = await _detect_mega_account_type(api)
            upload_timeout = 600 * premium_settings['timeout_multiplier']
            folder_timeout = 900 * premium_settings['timeout_multiplier']
        except Exception as e:
            LOGGER.warning(f"Could not detect account type: {e}, using free account settings")
            is_premium = False
            upload_timeout = 600
            folder_timeout = 900

        # Get upload destination folder
        upload_folder_node = mega_listener.node  # Root by default
        if Config.MEGA_UPLOAD_FOLDER:
            # TODO: Navigate to specific folder
            LOGGER.info(f"Using upload folder: {Config.MEGA_UPLOAD_FOLDER}")

    except Exception as e:
        error_msg = f"Error accessing MEGA for upload: {str(e)}"
        LOGGER.error(error_msg)
        await listener.on_upload_error(error_msg)
        await _cleanup_mega_upload_api(api, executor, mega_listener)
        return

    # Set name and size based on the path
    if ospath.isfile(path):
        listener.name = listener.name or ospath.basename(path)
        listener.size = await aiopath.getsize(path)
    else:
        listener.name = listener.name or ospath.basename(path)
        # Calculate folder size
        total_size = 0
        for dirpath, _, filenames in walk(path):
            for filename in filenames:
                filepath = ospath.join(dirpath, filename)
                if ospath.exists(filepath):
                    total_size += ospath.getsize(filepath)
        listener.size = total_size

    gid = token_hex(4)
    LOGGER.info(
        f"MEGA upload info - Name: {listener.name}, Size: {listener.size} bytes"
    )

    # Check if upload should be queued
    added_to_queue, event = await check_running_tasks(listener)
    if added_to_queue:
        LOGGER.info(f"MEGA upload added to queue: {listener.name}")
        async with task_dict_lock:
            task_dict[listener.mid] = QueueStatus(listener, gid, "up")
        await listener.on_upload_start()
        await send_status_message(listener.message)
        await event.wait()
        async with task_dict_lock:
            if listener.mid not in task_dict:
                LOGGER.info("MEGA upload was cancelled while in queue")
                await _cleanup_mega_upload_api(api, executor)
                return
        from_queue = True
        LOGGER.info(f"Starting queued MEGA upload: {listener.name}")
    else:
        from_queue = False

    # Create upload status tracker
    async with task_dict_lock:
        task_dict[listener.mid] = MegaUploadStatus(
            listener.name, listener.size, gid, mega_listener, listener.message
        )

    # Start the upload
    if from_queue:
        LOGGER.info(f"Resuming MEGA upload from queue: {listener.name}")
    else:
        await listener.on_upload_start()
        await send_status_message(listener.message)
        LOGGER.info(f"Starting MEGA upload: {listener.name}")

    try:
        # Generate thumbnail if it's a video file and thumbnail generation is enabled
        thumbnail_path = None
        if ospath.isfile(path) and Config.MEGA_UPLOAD_THUMBNAIL:
            thumbnail_path = await generate_video_thumbnail(path)
            if thumbnail_path:
                LOGGER.info(f"Generated thumbnail for MEGA upload: {thumbnail_path}")

        # Start the actual upload
        LOGGER.info(f"Starting MEGA transfer for: {listener.name}")

        try:
            if ospath.isfile(path):
                # Upload single file with MEGA SDK v4.8.0 API signature
                # startUpload(localPath, parent, fileName, mtime, appData, isSourceTemporary, startFirst, cancelToken)
                file_size = ospath.getsize(path)
                # Calculate timeout based on file size (minimum 120 seconds, max 3600 seconds)
                calculated_timeout = max(120, min(3600, file_size // (1024 * 1024) * 15))  # 15 seconds per MB
                actual_timeout = min(upload_timeout, calculated_timeout)
                LOGGER.info(f"Uploading file: {path} ({file_size} bytes) with timeout: {actual_timeout}s")

                await executor.do(
                    api.startUpload,
                    (path, upload_folder_node, listener.name, -1, None, False, False, None),
                    timeout=actual_timeout
                )
            else:
                # Upload folder with MEGA SDK v4.8.0 API signature
                # startUpload(localPath, parent, fileName, mtime, appData, isSourceTemporary, startFirst, cancelToken)
                LOGGER.info(f"Uploading folder: {path} with timeout: {folder_timeout}s")
                await executor.do(
                    api.startUpload,
                    (path, upload_folder_node, None, -1, None, False, False, None),
                    timeout=folder_timeout
                )

            LOGGER.info(f"MEGA upload completed: {listener.name}")
        except Exception as e:
            error_msg = f"MEGA upload operation failed: {str(e)}"
            LOGGER.error(error_msg)
            await listener.on_upload_error(error_msg)
            return

        # Clean up thumbnail if it was generated
        if thumbnail_path and ospath.exists(thumbnail_path):
            try:
                await aioremove(thumbnail_path)
                LOGGER.info(f"Cleaned up thumbnail: {thumbnail_path}")
            except Exception as e:
                LOGGER.error(f"Failed to clean up thumbnail: {e}")

    except Exception as e:
        error_msg = f"Error during MEGA upload: {str(e)}"
        LOGGER.error(error_msg)
        await listener.on_upload_error(error_msg)
    finally:
        # Always cleanup API connections
        await _cleanup_mega_upload_api(api, executor, mega_listener)
