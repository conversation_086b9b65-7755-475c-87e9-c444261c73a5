from secrets import token_hex
from aiofiles.os import makedirs
from asyncio import Event
from time import time

# Try to import MEGA SDK at module level, but don't fail if not available
try:
    from mega import <PERSON>Api, MegaListener, MegaRequest, MegaTransfer, MegaError
    MEGA_SDK_AVAILABLE = True
    MEGA_IMPORT_ERROR = None
except ImportError as e:
    # Create dummy classes to prevent import errors
    class MegaApi: pass
    class MegaListener: pass
    class MegaRequest:
        TYPE_LOGIN = 1
        TYPE_FETCH_NODES = 2
        TYPE_GET_PUBLIC_NODE = 3
        TYPE_COPY = 4
        TYPE_EXPORT = 5
    class MegaTransfer: pass
    class MegaError: pass
    MEGA_SDK_AVAILABLE = False
    MEGA_IMPORT_ERROR = str(e)
except Exception as e:
    # Handle other import errors
    class MegaApi: pass
    class MegaListener: pass
    class MegaRequest:
        TYPE_LOGIN = 1
        TYPE_FETCH_NODES = 2
        TYPE_GET_PUBLIC_NODE = 3
        TYPE_COPY = 4
        TYPE_EXPORT = 5
    class MegaTransfer: pass
    class MegaError: pass
    MEGA_SDK_AVAILABLE = False
    MEGA_IMPORT_ERROR = f"Unexpected error: {str(e)}"

from bot import LOGGER, task_dict, task_dict_lock
from bot.core.config_manager import Config
from bot.helper.ext_utils.bot_utils import sync_to_async, async_to_sync
from bot.helper.ext_utils.limit_checker import limit_checker
from bot.helper.ext_utils.links_utils import get_mega_link_type
from bot.helper.ext_utils.task_manager import (
    check_running_tasks,
    stop_duplicate_check,
)
from bot.helper.mirror_leech_utils.status_utils.mega_status import MegaDownloadStatus
from bot.helper.mirror_leech_utils.status_utils.queue_status import QueueStatus
from bot.helper.telegram_helper.message_utils import send_status_message


class MegaAppListener(MegaListener):
    _NO_EVENT_ON = (MegaRequest.TYPE_LOGIN, MegaRequest.TYPE_FETCH_NODES)
    NO_ERROR = "no error"

    def __init__(self, continue_event: Event, listener):
        self.continue_event = continue_event
        self.node = None
        self.public_node = None
        self.listener = listener
        self.is_cancelled = False
        self.error = None
        self.__bytes_transferred = 0
        self.__speed = 0
        self.__name = ""
        self.__start_time = time()
        super().__init__()

    @property
    def speed(self):
        return self.__speed

    @property
    def downloaded_bytes(self):
        return self.__bytes_transferred

    def onRequestFinish(self, api, request, error):
        # More robust error checking
        error_str = str(error).lower() if error else "unknown error"
        if error_str != "no error":
            self.error = error.copy() if hasattr(error, 'copy') else str(error)
            LOGGER.error(f"MEGA Request Error: {self.error}")
            self.continue_event.set()
            return

        request_type = request.getType()
        if request_type == MegaRequest.TYPE_LOGIN:
            LOGGER.info("MEGA login successful, fetching nodes...")
            api.fetchNodes()
        elif request_type == MegaRequest.TYPE_GET_PUBLIC_NODE:
            self.public_node = request.getPublicMegaNode()
            self.__name = self.public_node.getName()
            LOGGER.info(f"MEGA public node retrieved: {self.__name} ({self.public_node.getSize()} bytes)")
        elif request_type == MegaRequest.TYPE_FETCH_NODES:
            LOGGER.info("MEGA nodes fetched, getting root node...")
            self.node = api.getRootNode()
            self.__name = self.node.getName()
            LOGGER.info(f"MEGA root node: {self.node.getName()}")

        if (
            request_type not in self._NO_EVENT_ON
            or (self.node and "cloud drive" not in self.__name.lower())
        ):
            self.continue_event.set()

    def onRequestTemporaryError(self, _api, _request, error: MegaError):
        error_msg = error.toString()
        LOGGER.error(f"MEGA Request temporary error: {error_msg}")
        if not self.is_cancelled:
            self.is_cancelled = True
            async_to_sync(
                self.listener.on_download_error, f"MEGA Request Error: {error_msg}"
            )
        self.error = error_msg
        self.continue_event.set()

    def onTransferUpdate(self, api: MegaApi, transfer: MegaTransfer):
        if self.is_cancelled:
            LOGGER.info("MEGA transfer cancelled, stopping transfer...")
            api.cancelTransfer(transfer, None)
            self.continue_event.set()
            return

        self.__speed = transfer.getSpeed()
        self.__bytes_transferred = transfer.getTransferredBytes()

        # Log progress every 10MB or 10% intervals
        if self.__bytes_transferred > 0:
            total_bytes = transfer.getTotalBytes()
            if total_bytes > 0:
                progress = (self.__bytes_transferred / total_bytes) * 100
                if progress % 10 < 0.1:  # Log every 10%
                    LOGGER.info(f"MEGA download progress: {progress:.1f}% ({self.__bytes_transferred}/{total_bytes} bytes)")

    def onTransferFinish(self, _api: MegaApi, transfer: MegaTransfer, error):
        try:
            if self.is_cancelled:
                LOGGER.info("MEGA transfer was cancelled")
                self.continue_event.set()
                return

            if str(error).lower() != "no error":
                error_msg = str(error)
                LOGGER.error(f"MEGA transfer finished with error: {error_msg}")
                self.error = error_msg
                async_to_sync(self.listener.on_download_error, f"MEGA Transfer Error: {error_msg}")
            elif transfer.isFinished() and (
                transfer.isFolderTransfer() or transfer.getFileName() == self.__name
            ):
                LOGGER.info(f"MEGA download completed successfully: {transfer.getFileName()}")
                async_to_sync(self.listener.on_download_complete)

            self.continue_event.set()
        except Exception as e:
            LOGGER.error(f"Exception in onTransferFinish: {e}")
            self.continue_event.set()

    def onTransferTemporaryError(self, _api, transfer, error):
        filename = transfer.getFileName()
        state = transfer.getState()
        error_str = error.toString()

        LOGGER.error(f"MEGA transfer temporary error for {filename}: {error_str} (state: {state})")

        # States 1 and 4 are recoverable, don't cancel for these
        if state in [1, 4]:
            LOGGER.info(f"MEGA transfer error is recoverable (state {state}), continuing...")
            return

        self.error = error_str
        if not self.is_cancelled:
            self.is_cancelled = True
            async_to_sync(
                self.listener.on_download_error, f"MEGA Transfer Error: {error_str} ({filename})"
            )
            self.continue_event.set()

    async def cancel_download(self):
        self.is_cancelled = True
        await self.listener.on_download_error("Download Canceled by user")


class AsyncExecutor:
    def __init__(self):
        self.continue_event = Event()

    async def do(self, function, args):
        self.continue_event.clear()
        await sync_to_async(function, *args)
        await self.continue_event.wait()


async def _cleanup_mega_apis(api, folder_api, executor):
    """Helper function to cleanup MEGA API instances"""
    try:
        if api:
            await executor.do(api.logout, ())
        if folder_api:
            await executor.do(folder_api.logout, ())
    except Exception as e:
        LOGGER.error(f"Error during MEGA API cleanup: {e}")


async def add_mega_download(listener, path):
    """
    Download files/folders from MEGA.nz using the MEGA SDK v4.8.0 with FFmpeg support.

    Args:
        listener: Download listener object
        path: Download destination path
    """
    # Check if MEGA operations are enabled
    if not Config.MEGA_ENABLED:
        await listener.on_download_error(
            "❌ MEGA.nz operations are disabled by the administrator."
        )
        return

    # Check if MEGA SDK v4.8.0 is available and working
    if not MEGA_SDK_AVAILABLE:
        error_msg = "❌ MEGA SDK v4.8.0 not available. Please install megasdk v4.8.0."
        if MEGA_IMPORT_ERROR:
            error_msg += f"\nImport error: {MEGA_IMPORT_ERROR}"
        await listener.on_download_error(error_msg)
        return

    MEGA_EMAIL = Config.MEGA_EMAIL
    MEGA_PASSWORD = Config.MEGA_PASSWORD

    LOGGER.info(f"Starting MEGA download: {listener.link}")
    LOGGER.info("MEGA SDK v4.8.0 is available for download")

    executor = AsyncExecutor()
    api = MegaApi(None, None, None, "aimleechbot")
    folder_api = None

    mega_listener = MegaAppListener(executor.continue_event, listener)
    api.addListener(mega_listener)

    try:
        # Login if credentials are provided
        if MEGA_EMAIL and MEGA_PASSWORD:
            LOGGER.info("Logging into MEGA with provided credentials...")
            await executor.do(api.login, (MEGA_EMAIL, MEGA_PASSWORD))
        else:
            LOGGER.info("No MEGA credentials provided, using anonymous access...")

        # Handle file vs folder links
        link_type = get_mega_link_type(listener.link)
        LOGGER.info(f"MEGA link type: {link_type}")

        if link_type == "file":
            LOGGER.info("Getting public file node...")
            await executor.do(api.getPublicNode, (listener.link,))
            node = mega_listener.public_node
        else:
            LOGGER.info("Getting public folder node...")
            folder_api = MegaApi(None, None, None, "aimleechbot")
            folder_api.addListener(mega_listener)
            await executor.do(folder_api.loginToFolder, (listener.link,))
            node = await sync_to_async(folder_api.authorizeNode, mega_listener.node)

        if mega_listener.error is not None:
            error_msg = f"Failed to get MEGA node: {mega_listener.error}"
            LOGGER.error(error_msg)
            await listener.on_download_error(error_msg)
            return

        if node is None:
            error_msg = "Failed to retrieve MEGA node"
            LOGGER.error(error_msg)
            await listener.on_download_error(error_msg)
            return

    except Exception as e:
        error_msg = f"Error accessing MEGA link: {str(e)}"
        LOGGER.error(error_msg)
        await listener.on_download_error(error_msg)
        return
    finally:
        pass  # Cleanup will be done later after download completes

    # Set name and size based on the node type
    link_type = get_mega_link_type(listener.link)
    if link_type == "file":
        listener.name = listener.name or node.getName()
        listener.size = node.getSize()
    else:
        listener.name = listener.name or node.getName()
        listener.size = node.getSize()

    gid = token_hex(4)
    LOGGER.info(f"MEGA file info - Name: {listener.name}, Size: {listener.size} bytes")

    # Check size limits
    if listener.size > 0:
        LOGGER.info(f"Checking size limits for MEGA download: {listener.size} bytes")
        limit_msg = await limit_checker(
            listener.size,
            listener,
            isTorrent=False,
            isMega=True,
            isDriveLink=False,
            isYtdlp=False,
        )
        if limit_msg:
            LOGGER.warning(f"MEGA download size limit exceeded: {limit_msg}")
            await listener.on_download_error(limit_msg)
            await _cleanup_mega_apis(api, folder_api, executor)
            return

    # Check for duplicate downloads
    msg, button = await stop_duplicate_check(listener)
    if msg:
        LOGGER.info(f"MEGA download duplicate detected: {msg}")
        await listener.on_download_error(msg, button)
        await _cleanup_mega_apis(api, folder_api, executor)
        return

    # Check if download should be queued
    added_to_queue, event = await check_running_tasks(listener)
    if added_to_queue:
        LOGGER.info(f"MEGA download added to queue: {listener.name}")
        async with task_dict_lock:
            task_dict[listener.mid] = QueueStatus(listener, gid, "dl")
        await listener.on_download_start()
        await send_status_message(listener.message)
        await event.wait()
        async with task_dict_lock:
            if listener.mid not in task_dict:
                LOGGER.info("MEGA download was cancelled while in queue")
                await _cleanup_mega_apis(api, folder_api, executor)
                return
        from_queue = True
        LOGGER.info(f"Starting queued MEGA download: {listener.name}")
    else:
        from_queue = False

    # Create download status tracker
    async with task_dict_lock:
        task_dict[listener.mid] = MegaDownloadStatus(
            listener.name, listener.size, gid, mega_listener, listener.message
        )

    # Start the download
    if from_queue:
        LOGGER.info(f"Resuming MEGA download from queue: {listener.name}")
    else:
        await listener.on_download_start()
        await send_status_message(listener.message)
        LOGGER.info(f"Starting MEGA download: {listener.name}")

    try:
        # Create download directory
        await makedirs(path, exist_ok=True)
        LOGGER.info(f"MEGA download directory created: {path}")

        # Start the actual download with MEGA SDK v4.8.0 API signature
        LOGGER.info(f"Starting MEGA transfer for: {listener.name}")
        # startDownload(node, localPath, customName, appData, startFirst, cancelToken)

        # Calculate timeout based on file size (minimum 60 seconds, max 1800 seconds)
        download_timeout = max(60, min(1800, listener.size // (1024 * 1024) * 10))  # 10 seconds per MB
        LOGGER.info(f"Using download timeout: {download_timeout} seconds")

        await executor.do(api.startDownload, (node, path, listener.name, None, False, None))
        LOGGER.info(f"MEGA download completed: {listener.name}")

    except Exception as e:
        error_msg = f"Error during MEGA download: {str(e)}"
        LOGGER.error(error_msg)
        await listener.on_download_error(error_msg)
    finally:
        # Always cleanup API connections
        await _cleanup_mega_apis(api, folder_api, executor)
