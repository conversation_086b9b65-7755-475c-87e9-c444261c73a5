"""Constants of the MyJDownloader API."""

# API Documentation: https://my.jdownloader.org/developers

# MyJdownloader exception source
EXCEPTION_MYJD = "MYJD"
EXCEPTION_DEVICE = "DEVICE"

# MyJdownloader exception type
EXCEPTION_API_COMMAND_NOT_FOUND = "API_COMMAND_NOT_FOUND"
EXCEPTION_API_INTERFACE_NOT_FOUND = "API_INTERFACE_NOT_FOUND"
EXCEPTION_AUTH_FAILED = "AUTH_FAILED"
EXCEPTION_BAD_PARAMETERS = "BAD_PARAMETERS"
EXCEPTION_BAD_REQUEST = "BAD_REQUEST"
EXCEPTION_CHALLENGE_FAILED = "CHALLENGE_FAILED"
EXCEPTION_EMAIL_FORBIDDEN = "EMAIL_FORBIDDEN"
EXCEPTION_EMAIL_INVALID = "EMAIL_INVALID"
EXCEPTION_ERROR_EMAIL_NOT_CONFIRMED = "ERROR_EMAIL_NOT_CONFIRMED"
EXCEPTION_FAILED = "FAILED"
EXCEPTION_FILE_NOT_FOUND = "FILE_NOT_FOUND"
EXCEPTION_INTERNAL_SERVER_ERROR = "INTERNAL_SERVER_ERROR"
EXCEPTION_MAINTENANCE = "MAINTENANCE"
EXCEPTION_METHOD_FORBIDDEN = "METHOD_FORBIDDEN"
EXCEPTION_OFFLINE = "OFFLINE"
EXCEPTION_OUTDATED = "OUTDATED"
EXCEPTION_OVERLOAD = "OVERLOAD"
EXCEPTION_SESSION = "SESSION"
EXCEPTION_STORAGE_ALREADY_EXISTS = "STORAGE_ALREADY_EXISTS"
EXCEPTION_STORAGE_INVALID_KEY = "STORAGE_INVALID_KEY"
EXCEPTION_STORAGE_INVALID_STORAGEID = "STORAGE_INVALID_STORAGEID"
EXCEPTION_STORAGE_KEY_NOT_FOUND = "STORAGE_KEY_NOT_FOUND"
EXCEPTION_STORAGE_LIMIT_REACHED = "STORAGE_LIMIT_REACHED"
EXCEPTION_STORAGE_NOT_FOUND = "STORAGE_NOT_FOUND"
EXCEPTION_TOKEN_INVALID = "TOKEN_INVALID"
EXCEPTION_TOO_MANY_REQUESTS = "TOO_MANY_REQUESTS"
EXCEPTION_UNKNOWN = "UNKNOWN"
