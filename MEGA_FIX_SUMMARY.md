# MEGA SDK Virtual Environment Fix

## Problem Description

The MEGA SDK was being installed in the system Python environment during the base image build (`dev/Aeon` script), but the main application runs in a virtual environment created by `uv venv`. This caused the "No module named 'mega'" import error when the bot tried to use MEGA functionality.

### Root Cause Analysis

1. **Base Image Build** (`dev/Aeon`): 
   - Installs MEGA SDK to system Python: `/usr/local/lib/python3.13/dist-packages/mega`
   - Uses `python3 -m pip install --break-system-packages`

2. **Main Dockerfile**:
   - Creates virtual environment: `uv venv` (creates `.venv`)
   - Installs dependencies: `uv pip install -r requirements.txt`

3. **Runtime** (`start.sh`):
   - Activates virtual environment: `source .venv/bin/activate`
   - Runs bot: `python3 -m bot`

4. **Issue**: Virtual environment doesn't have access to system-installed MEGA SDK

## Solution Implemented

Modified `dev/Aeon` script to install MEGA SDK directly into the virtual environment that the bot expects:

### Key Changes

1. **Create Virtual Environment in Correct Location**:
   ```bash
   mkdir -p /usr/src/app
   cd /usr/src/app
   /usr/bin/uv venv .venv
   ```

2. **Install MEGA SDK in Virtual Environment**:
   ```bash
   source /usr/src/app/.venv/bin/activate
   python3 -m pip install megasdk-4.8.0-py2.py3-none-any.whl
   ```

3. **Preserve Shared Libraries**:
   - Still copy `libmega.so` to system library path for system-level access
   - Fallback to system installation if needed for shared libraries

4. **Verification**:
   - Test MEGA SDK import in virtual environment
   - Verify virtual environment structure
   - Comprehensive error reporting

### File Changes

- **Modified**: `dev/Aeon` (lines 66-208)
  - Updated `install_mega_sdk()` function
  - Added virtual environment creation and activation
  - Updated paths to match `/usr/src/app/.venv`
  - Added comprehensive verification steps

## How It Works

1. **Base Image Build**:
   - `dev/Aeon` creates `/usr/src/app/.venv` virtual environment
   - Installs MEGA SDK into this virtual environment
   - Copies shared libraries to system paths

2. **Main Dockerfile**:
   - Sets `WORKDIR /usr/src/app`
   - Runs `uv venv` (which will use existing `.venv` or create new one)
   - Installs other dependencies with `uv pip install`

3. **Runtime**:
   - `start.sh` activates `.venv/bin/activate`
   - Bot runs with access to MEGA SDK in virtual environment

## Testing

### Local Testing
```bash
# Test the fix locally (if you have the wheel file)
cd /home/<USER>/Documents/aimleechbot
python3 test_mega_fix.py
```

### Docker Testing
```bash
# Build and test the base image
docker build -f dev/Dockerfile -t test-mega-fix .

# Run container and test MEGA import
docker run -it test-mega-fix bash
cd /usr/src/app
source .venv/bin/activate
python3 -c "import mega; print('MEGA SDK available:', mega.MegaApi('test').getVersion())"
```

### Production Testing
1. Build new base image using GitHub workflow
2. Deploy to Heroku
3. Test `/leech` command with MEGA link

## Expected Results

After this fix:
- ✅ MEGA SDK will be available in the virtual environment
- ✅ Bot will successfully import `mega` module
- ✅ `/leech` command will work with MEGA links
- ✅ No more "No module named 'mega'" errors

## Verification Commands

In the running container:
```bash
# Check virtual environment
source .venv/bin/activate
which python3
python3 -c "import sys; print('Virtual env:', sys.prefix)"

# Test MEGA import
python3 -c "
import mega
api = mega.MegaApi('test')
print('✅ MEGA SDK version:', api.getVersion())
print('✅ Module location:', mega.__file__)
"
```

## Rollback Plan

If issues occur, the original `dev/Aeon` script can be restored by reverting the `install_mega_sdk()` function to install in system Python environment with `--break-system-packages`.

## Notes

- Virtual environment is preserved during cleanup
- Shared libraries are still copied to system paths for compatibility
- Fallback system installation is maintained for robustness
- All paths are absolute to avoid working directory issues
