#!/usr/bin/env python3
"""
Test script to verify MEGA SDK installation in virtual environment
This script simulates what the bot does when it tries to import MEGA
"""

import sys
import os

def test_mega_import():
    """Test MEGA SDK import and basic functionality"""
    print("🔍 Testing MEGA SDK import...")
    print(f"Python executable: {sys.executable}")
    print(f"Python version: {sys.version}")
    print(f"Virtual environment: {os.environ.get('VIRTUAL_ENV', 'Not in venv')}")
    
    # Test import
    try:
        import mega
        print("✅ MEGA module imported successfully")
        
        # Test basic API creation
        api = mega.MegaApi('test')
        print(f"✅ MEGA API created successfully")
        print(f"API version: {api.getVersion()}")
        print(f"MEGA module location: {mega.__file__}")
        
        # Test available methods
        methods = [m for m in dir(api) if not m.startswith('_')]
        print(f"Available API methods: {len(methods)}")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Other error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_virtual_environment():
    """Test if we're in the correct virtual environment"""
    print("\n🔍 Testing virtual environment setup...")
    
    # Check if we're in a virtual environment
    venv_path = os.environ.get('VIRTUAL_ENV')
    if venv_path:
        print(f"✅ In virtual environment: {venv_path}")
    else:
        print("⚠️  Not in virtual environment")
    
    # Check Python paths
    print("Python import paths:")
    for i, path in enumerate(sys.path):
        if path:
            print(f"  {i}: {path}")
    
    return venv_path is not None

if __name__ == "__main__":
    print("🧪 MEGA SDK Virtual Environment Test")
    print("=" * 50)
    
    venv_ok = test_virtual_environment()
    mega_ok = test_mega_import()
    
    print("\n" + "=" * 50)
    if venv_ok and mega_ok:
        print("✅ All tests passed! MEGA SDK should work correctly.")
        sys.exit(0)
    else:
        print("❌ Some tests failed. Check the output above.")
        sys.exit(1)
