Bot commands to be set in <a href="https://t.me/BotFather">@BotFather</a>

```
start - Start the bot
mirror - or /m Mirror
jdmirror - or /jm Mirror using jdownloader
nzbmirror - or /nm Mirror using sabnzbd
ytdl - or /y Mirror yt-dlp supported links
streamripmirror - or /srmirror or /srm Mirror music from streaming platforms
zotifymirror - or /zmirror or /zm Mirror music from Spotify
leech - or /l Upload to telegram
jdleech - or /jl Leech using jdownloader
nzbleech - or /nl Leech using sabnzbd
ytdlleech - or /yl Leech yt-dlp supported links
streamripleech - or /srleech or /srl Leech music from streaming platforms
zotifyleech - or /zleech or /zl Leech music from Spotify
clone - Copy file/folder to Drive
mediainfo - or /mi Check mediainfo
count - Count file/folder from GDrive
del - Delete file/folder from GDrive
cancelall - Cancel all tasks
forcestart - or /fs Start task from queue
list - Search files in Drive
search - Search for torrents with API
nzbsearch - Search for NZB files
status - or /s Get Mirror Status message (also /statusall, /sall)
users - Show authorized users
auth - Authorize user or chat
unauth - Unauthorize user or chat
addsudo - Add sudo user
rmsudo - Remove sudo user
ping - Ping the Bot
restart - Restart the Bot (also /restartall)
stats - Bot Usage Stats
help - All cmds with description
log - Get the Bot Log
shell - Run commands in Shell
aexec - Execute async function
exec - Execute sync function
clearlocals - Clear local variables
botsettings - Bot settings
settings - or /usettings or /us User settings
speedtest - Run speedtest
broadcast - Broadcast message (also /broadcastall)
sel - Select files from torrent
rss - RSS menu
fontstyles - or /fonts View available font styles for leech
check_deletions - or /cd Check deletions
imdb - Search for movies or TV series info
login - Login to the bot using password for permanent access
mediasearch - or /mds Search for media files
mediatools - or /mt Media tools settings for watermark and other features
mthelp - or /mth View detailed help for media tools features
gensession - or /gs Generate Pyrogram session string securely
truecaller - Lookup phone numbers using Truecaller
ask - Chat with AI using the bot
spectrum - or /sox Generate spectrum from audio
paste - Paste text to katb.in website
virustotal - Scan files or URLs for viruses using VirusTotal
streamripsearch - or /srsearch or /srs Search music across platforms
zotifysearch - or /zsearch or /zs Search music on Spotify
```
