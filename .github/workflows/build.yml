name: Push to docker.io

on:
  workflow_dispatch:
    inputs:
      username:
        description: "Docker Hub Username"
        required: true
      password:
        description: "Docker Hub Password"
        required: true
        type: string
      image_name:
        description: "Docker Image Name"
        required: true
      amd64_only:
        description: "Build only for amd64"
        required: false
        type: boolean
        default: false
  schedule:
    # Run every 24 hours at 00:00 UTC
    - cron: '0 0 * * *'

jobs:
  build:
    runs-on: ubuntu-24.04
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to Docker.io
        uses: docker/login-action@v3
        with:
          username: ${{ github.event_name == 'workflow_dispatch' && inputs.username || secrets.DOCKER_USERNAME }}
          password: ${{ github.event_name == 'workflow_dispatch' && inputs.password || secrets.DOCKER_PASSWORD }}

      - name: Docker Metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          tags: latest
          images: ${{ github.event_name == 'workflow_dispatch' && format('{0}/{1}', inputs.username, inputs.image_name) || format('{0}/{1}', secrets.DOCKER_USERNAME, vars.DOCKER_IMAGE_NAME) }}

      - name: Prepare Docker Build Files
        run: |
          cp dev/Aeon .
          cp dev/megasdk-4.8.0-py2.py3-none-any.whl .
          chmod +x Aeon

      - name: Build and Push
        uses: docker/build-push-action@v5
        with:
          push: true
          no-cache: true
          provenance: false
          context: .
          file: ./dev/Dockerfile
          platforms: ${{ ((github.event_name == 'workflow_dispatch' && inputs.amd64_only == true) || (github.event_name == 'schedule' && vars.AMD64_ONLY == 'true')) && 'linux/amd64' || 'linux/arm64,linux/amd64' }}
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          annotations: ${{ steps.meta.outputs.annotations }}
