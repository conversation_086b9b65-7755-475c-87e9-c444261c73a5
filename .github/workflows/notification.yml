name: Telegram Notification

on: [push]

jobs:
  notify:
    runs-on: ubuntu-latest
    steps:
      - name: Send Telegram Notification
        env:
          DEFAULT_CHAT_ID: "-1001964570396"
          MAIN_CHAT_ID: "-1001980878451"
          TELEGRAM_BOT_TOKEN: ${{ secrets.BOT_TOKEN }}
          REPO: ${{ github.repository }}
          BRANCH_NAME: ${{ github.ref_name }}
          USER: ${{ github.actor }}
          AUTHOR_URL: https://github.com/${{ github.actor }}
          COMMITS_JSON: ${{ toJson(github.event.commits) }}
        run: |
          if [[ "$REPO" != "AeonOrg/Aeon-MLTB" ]]; then
            echo "Repository is not the original. Skipping Telegram notification."
            exit 0
          fi

          CHAT_ID=$DEFAULT_CHAT_ID
          if [[ "$BRANCH_NAME" == "main" ]]; then
            CHAT_ID=$MAIN_CHAT_ID
          fi

          REPO_URL="https://github.com/${REPO}"
          BRANCH_URL="${REPO_URL}/commits/${BRANCH_NAME}"

          MESSAGE="*New Commit Pushed*\n\n"
          MESSAGE+="*Commit by:* [${USER}](${AUTHOR_URL})\n"
          MESSAGE+="*Repository:* [${REPO}](${REPO_URL})\n"
          MESSAGE+="*Branch:* [${BRANCH_NAME}](${BRANCH_URL})\n\n"

          mapfile -t COMMITS < <(echo "$COMMITS_JSON" | jq -c '.[]')
          for commit in "${COMMITS[@]}"; do
            HASH=$(jq -r '.id' <<< "$commit")
            FULL_MSG=$(jq -r '.message' <<< "$commit")
            MSG_TITLE=$(echo "$FULL_MSG" | head -n 1)
            MSG_BODY=$(echo "$FULL_MSG" | tail -n +2)
            SHORT_HASH=${HASH:0:7}
            COMMIT_URL="${REPO_URL}/commit/${HASH}"
            MESSAGE+="[${SHORT_HASH}](${COMMIT_URL}): ${MSG_TITLE}\n"
            if [[ -n "$MSG_BODY" ]]; then
              MESSAGE+="${MSG_BODY}\n"
            fi
          done

          curl -s -X POST "https://api.telegram.org/bot${TELEGRAM_BOT_TOKEN}/sendMessage" \
          -H "Content-Type: application/json" \
          -d '{
            "chat_id": "'"${CHAT_ID}"'",
            "text": "'"${MESSAGE//\"/\\\"}"'",
            "parse_mode": "Markdown",
            "disable_web_page_preview": true
          }'