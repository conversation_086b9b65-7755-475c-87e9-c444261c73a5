name: Deploy to <PERSON><PERSON>

on:
  # Manual deployment trigger - only available on extended branch
  workflow_dispatch:
    branches:
      - extended  # Manual trigger only available on extended branch
    inputs:
      HEROKU_APP_NAME:
        description: "Heroku App name"
        required: true
      HEROKU_API_KEY:
        description: "Heroku API key"
        required: true
      HEROKU_EMAIL:
        description: "Heroku email address"
        required: true
      HEROKU_TEAM_NAME:
        description: "Heroku Team Name"
        required: false
      BOT_TOKEN:
        description: "Telegram bot token"
        required: true
      OWNER_ID:
        description: "Owner's telegram ID"
        required: true
      DATABASE_URL:
        description: "Database URL from MongoDB"
        required: true
      TELEGRAM_API:
        description: "Telegram API ID from https://my.telegram.org/"
        required: true
      TELEGRAM_HASH:
        description: "Telegram HASH from https://my.telegram.org/"
        required: true

  # Automatic deployment when requirements.txt or Dockerfile is updated
  push:
    paths:
      - 'requirements.txt'
      - 'Dockerfile'
    branches:
      - extended  # Deploy from extended branch only

  # Monthly scheduled deployment at 3 AM UTC on the 1st of every month
  schedule:
    - cron: '0 3 1 * *'

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: extended  # Always checkout extended branch

      - name: Delete unnecessary directories and files
        run: |
          # Directories to delete
          for dir in bot qBittorrent docs web; do
            if [ -d "./$dir" ]; then
              echo "Deleting directory: $dir"
              rm -rf ./$dir
            else
              echo "Directory not found: $dir"
            fi
          done

          # Delete aria.sh if it exists
          if [ -f "./aria.sh" ]; then
            echo "Deleting file: aria.sh"
            rm -f ./aria.sh
          else
            echo "File not found: aria.sh"
          fi

      - name: Deploy to Heroku
        uses: 5hojib/heroku-deploy-action@v2
        with:
          # Use inputs for manual deployment, secrets for automatic deployment
          heroku_api_key: ${{ inputs.HEROKU_API_KEY || secrets.HEROKU_API_KEY }}
          heroku_app_name: ${{ inputs.HEROKU_APP_NAME || secrets.HEROKU_APP_NAME }}
          heroku_email: ${{ inputs.HEROKU_EMAIL || secrets.HEROKU_EMAIL }}
          team: ${{ inputs.HEROKU_TEAM_NAME || secrets.HEROKU_TEAM_NAME || '' }}
          usedocker: true
          docker_heroku_process_type: web
          stack: "container"
          region: "eu"
        env:
          # Use inputs for manual deployment, secrets for automatic deployment
          HD_OWNER_ID: ${{ inputs.OWNER_ID || secrets.OWNER_ID }}
          HD_TELEGRAM_API: ${{ inputs.TELEGRAM_API || secrets.TELEGRAM_API }}
          HD_TELEGRAM_HASH: ${{ inputs.TELEGRAM_HASH || secrets.TELEGRAM_HASH }}
          HD_DATABASE_URL: ${{ inputs.DATABASE_URL || secrets.DATABASE_URL }}
          HD_BOT_TOKEN: ${{ inputs.BOT_TOKEN || secrets.BOT_TOKEN }}
          HD_HEROKU_APP_NAME: ${{ inputs.HEROKU_APP_NAME || secrets.HEROKU_APP_NAME }}
          HD_HEROKU_API_KEY: ${{ inputs.HEROKU_API_KEY || secrets.HEROKU_API_KEY }}
