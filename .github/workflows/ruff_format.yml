name: format code with ruff

on:
  push:
    branches: [main]

jobs:
  code-format:
    permissions:
      contents: write
    runs-on: ubuntu-24.04
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.13"

      - name: Install the latest version of uv
        uses: astral-sh/setup-uv@v3
        with:
          version: "latest"

      - name: Run ruff to lint and format code
        run: |
          uv tool run ruff check . --exit-zero
          uv tool run ruff format .
          git add -u

      - name: Commit and push changes
        id: commit
        run: |
          git config --global user.name "5hojib"
          git config --global user.email "<EMAIL>"

          if git diff-index --quiet HEAD --; then
            echo "no_changes=true" >> $GITHUB_ENV
            echo "No changes to commit."
          else
            git commit -s -m "format: auto-format code by ruff."
            git push origin ${{ github.ref }}
            echo "no_changes=false" >> $GITHUB_ENV
          fi
        env:
          GITHUB_TOKEN: ${{ secrets.GX_TOKEN }}

      - name: Send Telegram Notification
        if: env.no_changes == 'false'
        env:
          TELEGRAM_CHAT_ID: "-1001964570396"
          TELEGRAM_BOT_TOKEN: ${{ secrets.BOT_TOKEN }}
          REPO: ${{ github.repository }}
          BRANCH_NAME: ${{ github.ref_name }}
          USER: ${{ github.actor }}
          AUTHOR_URL: https://github.com/${{ github.actor }}
        run: |
          COMMIT_HASH=$(git rev-parse HEAD)
          COMMIT_URL="https://github.com/${REPO}/commit/${COMMIT_HASH}"
          FULL_MSG=$(git log -1 --pretty=%B)
          MSG_TITLE=$(echo "$FULL_MSG" | head -n 1)
          MSG_BODY=$(echo "$FULL_MSG" | tail -n +2)

          MESSAGE="*New Commit Pushed*\n\n"
          MESSAGE+="*Commit by:* [${USER}](${AUTHOR_URL})\n"
          MESSAGE+="*Repository:* ${REPO}\n"
          MESSAGE+="*Branch:* ${BRANCH_NAME}\n\n"
          MESSAGE+="[${COMMIT_HASH:0:7}](${COMMIT_URL}): ${MSG_TITLE}\n"
          if [[ -n "$MSG_BODY" ]]; then
            MESSAGE+="${MSG_BODY}\n"
          fi

          curl -s -X POST "https://api.telegram.org/bot${TELEGRAM_BOT_TOKEN}/sendMessage" \
          -H "Content-Type: application/json" \
          -d '{
            "chat_id": "'"${TELEGRAM_CHAT_ID}"'",
            "text": "'"${MESSAGE//\"/\\\"}"'",
            "parse_mode": "Markdown",
            "disable_web_page_preview": true
          }'