#!/bin/bash
# Aeon

set -euo pipefail

export DEBIAN_FRONTEND=noninteractive

TEMP_DIR=$(mktemp -d)
JDOWNLOADER_HOME="/JDownloader"
BIN_DIR="/usr/local/bin"

download_files() {
    mkdir -p "$JDOWNLOADER_HOME" "$BIN_DIR"
    local arch=$(uname -m)
    
    if [ "$arch" = "x86_64" ]; then
        wget -qO "$BIN_DIR/xnox" "https://github.com/userdocs/qbittorrent-nox-static/releases/latest/download/x86_64-qbittorrent-nox" &
        wget -qO "$TEMP_DIR/ffmpeg.tar.xz" "https://github.com/5hojib/FFmpeg-Builds/releases/download/latest/ffmpeg-n7.1-latest-linux64-gpl-7.1.tar.xz" &
        wget -qO "$TEMP_DIR/uv.tar.gz" "https://github.com/astral-sh/uv/releases/latest/download/uv-x86_64-unknown-linux-gnu.tar.gz" &
    else
        wget -qO "$BIN_DIR/xnox" "https://github.com/userdocs/qbittorrent-nox-static/releases/latest/download/aarch64-qbittorrent-nox" &
        wget -qO "$TEMP_DIR/ffmpeg.tar.xz" "https://github.com/5hojib/FFmpeg-Builds/releases/latest/download/ffmpeg-n7.1-latest-linuxarm64-gpl-7.1.tar.xz" &
        wget -qO "$TEMP_DIR/uv.tar.gz" "https://github.com/astral-sh/uv/releases/latest/download/uv-aarch64-unknown-linux-gnu.tar.gz" &
    fi
    wget -qO "$JDOWNLOADER_HOME/JDownloader.jar" http://installer.jdownloader.org/JDownloader.jar &
    
    wait
}

install_packages() {
    apt-get update
    apt-get upgrade -y
    apt-get install -y --no-install-recommends \
        python3.13 \
        libpython3.13 \
        python3-pip \
        sabnzbdplus \
        aria2 \
        sox \
        libsox-fmt-all \
        curl \
        zstd \
        xz-utils \
        git \
        libmagic-dev \
        p7zip-full \
        p7zip-rar \
        tzdata \
        wget \
        openjdk-21-jre \
        openjdk-21-jre-headless \
        libcares2 \
        libcurl4 \
        libuv1 \
        libcrypto++8 \
        libsodium23 \
        libsqlite3-0 \
        libssl3 \
        libfreeimage3 \
        zlib1g

    ln -sf /usr/bin/python3.13 /usr/bin/python3
    ln -sf /usr/bin/python3.13 /usr/bin/python
}

install_mega_sdk() {
    echo "🔧 Installing MEGA SDK v4.8.0 from pre-built wheel..."

    # Verify Python environment
    echo "🔍 Checking Python environment..."
    python3 --version

    # Check if wheel file exists in current directory (copied by Dockerfile)
    WHEEL_FILE="./megasdk-4.8.0-py2.py3-none-any.whl"
    if [ ! -f "$WHEEL_FILE" ]; then
        echo "❌ MEGA SDK wheel file not found at $WHEEL_FILE"
        echo "🔍 Looking for wheel file in current directory..."
        find . -name "*.whl" -type f | head -5
        exit 1
    fi

    echo "✅ Found MEGA SDK wheel file: $WHEEL_FILE"

    # Install setuptools first
    echo "📦 Installing setuptools..."
    python3 -m pip install setuptools --break-system-packages || {
        echo "❌ Failed to install setuptools"
        exit 1
    }

    # Install MEGA SDK from wheel to system packages
    echo "📦 Installing MEGA SDK v4.8.0 from wheel to system packages..."
    python3 -m pip install "$WHEEL_FILE" --break-system-packages || {
        echo "❌ Failed to install MEGA SDK from wheel"
        exit 1
    }

    # Copy shared libraries to system library path
    echo "🔗 Setting up MEGA SDK shared libraries..."
    MEGA_PKG_DIR="/usr/local/lib/python3.13/dist-packages/mega"
    if [ -f "$MEGA_PKG_DIR/libmega.so" ]; then
        cp "$MEGA_PKG_DIR/libmega.so" /usr/lib/x86_64-linux-gnu/libmega.so.40800
        echo "✅ Copied libmega.so to system library path"
    fi

    # Update library cache
    ldconfig

    # Create a global site-packages directory that will be accessible to virtual environments
    echo "🔧 Setting up MEGA SDK for virtual environment compatibility..."
    GLOBAL_SITE_PACKAGES="/usr/local/lib/python3.13/site-packages"
    mkdir -p "$GLOBAL_SITE_PACKAGES"

    # Copy the mega package to the global site-packages directory
    if [ -d "$MEGA_PKG_DIR" ]; then
        cp -r "$MEGA_PKG_DIR" "$GLOBAL_SITE_PACKAGES/"
        echo "✅ Copied MEGA SDK to global site-packages for virtual environment access"
    fi

    # Create a .pth file to ensure the mega module is always available
    echo "🔧 Creating .pth file for MEGA SDK accessibility..."
    echo "/usr/local/lib/python3.13/dist-packages" > "$GLOBAL_SITE_PACKAGES/mega-sdk.pth"
    echo "✅ Created .pth file for MEGA SDK"

    # Also install to the default user site-packages location
    echo "📦 Installing MEGA SDK to user site-packages as backup..."
    python3 -m pip install "$WHEEL_FILE" --user --force-reinstall || {
        echo "⚠️ Warning: Failed to install MEGA SDK to user site-packages (non-critical)"
    }

    # Verify installation in system environment
    echo "🔍 Verifying MEGA SDK installation in system environment..."
    python3 -c "
import mega
api = mega.MegaApi('test')
print('✅ MEGA SDK v4.8.0 imported successfully in system environment')
print(f'API version: {api.getVersion()}')
print(f'API methods available: {len([m for m in dir(api) if not m.startswith(\"_\")])}')
" || {
        echo "❌ MEGA SDK installation verification failed"
        echo "🔍 Debugging import issue..."

        # Check installed packages
        echo "📦 Installed MEGA packages:"
        pip3 list | grep -i mega || echo "No MEGA packages found"

        # Check Python import path
        echo "🐍 Python import paths:"
        python3 -c "import sys; [print(f'  {p}') for p in sys.path if p]"

        # Try to import and show error
        echo "🔍 Import error details:"
        python3 -c "
try:
    import mega
    print('✅ Import successful')
except Exception as e:
    print(f'❌ Import failed: {e}')
    import traceback
    traceback.print_exc()
"
        exit 1
    }

    # Test virtual environment compatibility
    echo "🔍 Testing MEGA SDK accessibility in a virtual environment..."
    TEMP_VENV="/tmp/test_venv"
    python3 -m venv "$TEMP_VENV"
    source "$TEMP_VENV/bin/activate"

    # Test import in virtual environment
    python3 -c "
import sys
print('🐍 Virtual environment Python paths:')
[print(f'  {p}') for p in sys.path if p]
print()
try:
    import mega
    api = mega.MegaApi('test')
    print('✅ MEGA SDK v4.8.0 accessible in virtual environment')
    print(f'API version: {api.getVersion()}')
except Exception as e:
    print(f'❌ MEGA SDK not accessible in virtual environment: {e}')
    # This is expected, we'll handle it in the main Dockerfile
    print('📝 Note: This will be resolved when the main application installs the wheel in its virtual environment')
" || echo "⚠️ Virtual environment test completed (expected to show import issues)"

    deactivate
    rm -rf "$TEMP_VENV"

    echo "✅ MEGA SDK v4.8.0 installed successfully from wheel"
    echo "📝 Note: Virtual environments will need to install the wheel separately or use system packages"
}

create_mega_venv_installer() {
    echo "🔧 Creating MEGA SDK virtual environment installer script..."

    # Create a script that can be used by the main Dockerfile to install MEGA SDK in virtual environments
    cat > /usr/local/bin/install-mega-venv.sh << 'EOF'
#!/bin/bash
# MEGA SDK Virtual Environment Installer
# This script installs MEGA SDK v4.8.0 in the current virtual environment

set -euo pipefail

echo "🔧 Installing MEGA SDK v4.8.0 in virtual environment..."

# Check if we're in a virtual environment
if [ -z "${VIRTUAL_ENV:-}" ]; then
    echo "❌ Not in a virtual environment. Please activate a virtual environment first."
    exit 1
fi

echo "✅ Virtual environment detected: $VIRTUAL_ENV"

# Look for the MEGA SDK wheel file
WHEEL_FILE=""
for location in "/megasdk-4.8.0-py2.py3-none-any.whl" "./megasdk-4.8.0-py2.py3-none-any.whl" "/usr/src/app/megasdk-4.8.0-py2.py3-none-any.whl"; do
    if [ -f "$location" ]; then
        WHEEL_FILE="$location"
        break
    fi
done

if [ -z "$WHEEL_FILE" ]; then
    echo "❌ MEGA SDK wheel file not found. Trying to copy from system..."
    # Try to find and copy the wheel file
    if [ -f "/megasdk-4.8.0-py2.py3-none-any.whl" ]; then
        cp "/megasdk-4.8.0-py2.py3-none-any.whl" "./megasdk-4.8.0-py2.py3-none-any.whl"
        WHEEL_FILE="./megasdk-4.8.0-py2.py3-none-any.whl"
    else
        echo "❌ MEGA SDK wheel file not found anywhere"
        exit 1
    fi
fi

echo "✅ Found MEGA SDK wheel file: $WHEEL_FILE"

# Install setuptools first if not present
python -c "import setuptools" 2>/dev/null || {
    echo "📦 Installing setuptools in virtual environment..."
    pip install setuptools
}

# Install MEGA SDK from wheel
echo "📦 Installing MEGA SDK v4.8.0 from wheel in virtual environment..."
pip install "$WHEEL_FILE" --force-reinstall || {
    echo "❌ Failed to install MEGA SDK from wheel in virtual environment"
    exit 1
}

# Copy shared libraries if they exist in system
echo "🔗 Setting up MEGA SDK shared libraries for virtual environment..."
SYSTEM_MEGA_DIR="/usr/local/lib/python3.13/dist-packages/mega"
VENV_MEGA_DIR="$VIRTUAL_ENV/lib/python3.13/site-packages/mega"

if [ -f "$SYSTEM_MEGA_DIR/libmega.so" ] && [ -d "$VENV_MEGA_DIR" ]; then
    cp "$SYSTEM_MEGA_DIR/libmega.so" "$VENV_MEGA_DIR/" 2>/dev/null || true
    echo "✅ Copied libmega.so to virtual environment"
fi

# Verify installation
echo "🔍 Verifying MEGA SDK installation in virtual environment..."
python -c "
import mega
api = mega.MegaApi('test')
print('✅ MEGA SDK v4.8.0 imported successfully in virtual environment')
print(f'API version: {api.getVersion()}')
print(f'API methods available: {len([m for m in dir(api) if not m.startswith(\"_\")])}')
" || {
    echo "❌ MEGA SDK installation verification failed in virtual environment"
    echo "🔍 Debugging import issue..."

    # Check installed packages
    echo "📦 Installed MEGA packages in virtual environment:"
    pip list | grep -i mega || echo "No MEGA packages found"

    # Check Python import path
    echo "🐍 Virtual environment Python import paths:"
    python -c "import sys; [print(f'  {p}') for p in sys.path if p]"

    # Try to import and show error
    echo "🔍 Import error details:"
    python -c "
try:
    import mega
    print('✅ Import successful')
except Exception as e:
    print(f'❌ Import failed: {e}')
    import traceback
    traceback.print_exc()
"
    exit 1
}

echo "✅ MEGA SDK v4.8.0 installed successfully in virtual environment"
EOF

    chmod +x /usr/local/bin/install-mega-venv.sh
    echo "✅ Created MEGA SDK virtual environment installer script at /usr/local/bin/install-mega-venv.sh"

    # Also copy the wheel file to a standard location for easy access
    if [ -f "./megasdk-4.8.0-py2.py3-none-any.whl" ]; then
        cp "./megasdk-4.8.0-py2.py3-none-any.whl" "/megasdk-4.8.0-py2.py3-none-any.whl"
        echo "✅ Copied MEGA SDK wheel to /megasdk-4.8.0-py2.py3-none-any.whl for easy access"
    fi
}

# Process and move files
process_files() {
    chmod 700 "$BIN_DIR/xnox"
    
    # Extract FFmpeg
    tar -xf "$TEMP_DIR/ffmpeg.tar.xz" -C "$TEMP_DIR"
    local ffmpeg_dir=$(find "$TEMP_DIR" -type d -name "ffmpeg-n7.1-latest-linux*" | head -n 1)
    mv "$ffmpeg_dir/bin/ffmpeg" /usr/bin/xtra
    mv "$ffmpeg_dir/bin/ffprobe" /usr/bin/ffprobe
    mv "$ffmpeg_dir/bin/ffplay" /usr/bin/ffplay
    chmod +x /usr/bin/xtra /usr/bin/ffprobe /usr/bin/ffplay
    
    # Extract UV
    tar -xzf "$TEMP_DIR/uv.tar.gz" -C "$TEMP_DIR"
    mv $(find "$TEMP_DIR" -type f -name "uv" | head -n 1) /usr/bin/uv
    chmod +x /usr/bin/uv
    
    # Install rclone
    curl https://rclone.org/install.sh | bash
    mv /usr/bin/rclone /usr/bin/xone
    mv /usr/bin/aria2c /usr/bin/xria
    # mv /usr/bin/ffmpeg /usr/bin/xtra
    mv /usr/bin/sabnzbdplus /usr/bin/xnzb
}

cleanup() {
    apt-get purge -y wget \
        perl \
        xz-utils \
        perl-modules-5.40
    apt-get install -y --no-install-recommends \
        git \
        curl \
        cpulimit
    apt-get autoremove -y --purge
    apt-get autoclean -y
    apt-get clean -y

    # Preserve MEGA SDK files and installer script
    echo "🔧 Preserving MEGA SDK files for virtual environment use..."

    rm -rf \
        /var/lib/apt/lists/* \
        "$TEMP_DIR" \
        Aeon \
        Dockerfile \
        ~/.cache \
        /tmp/* \
        /var/tmp/*

    # Keep the MEGA SDK wheel file and installer script
    # /megasdk-4.8.0-py2.py3-none-any.whl - preserved
    # /usr/local/bin/install-mega-venv.sh - preserved

    echo "✅ Cleanup completed, MEGA SDK files preserved"
}

main() {
    install_packages
    download_files
    process_files
    install_mega_sdk
    create_mega_venv_installer
    cleanup
}

main